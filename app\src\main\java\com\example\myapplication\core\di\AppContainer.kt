package com.example.myapplication.core.di

import com.example.myapplication.core.constants.AppConstants
import com.example.myapplication.data.remote.api.ApiService
import com.example.myapplication.data.repository.AuthRepositoryImpl
import com.example.myapplication.domain.repository.AuthRepository
import com.example.myapplication.domain.useCase.LoginUseCase
import com.example.myapplication.domain.useCase.RegisterUseCase
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * Container manual para Dependency Injection
 * 
 * Seguindo DIP: Dependemos de abstrações (interfaces) não de implementações concretas
 * Seguindo SRP: Responsabilidade única de prover dependências
 */
object AppContainer {
    
    // Network dependencies
    private val okHttpClient: OkHttpClient by lazy {
        OkHttpClient.Builder()
            .addInterceptor(HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            })
            .connectTimeout(AppConstants.Api.TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(AppConstants.Api.TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(AppConstants.Api.TIMEOUT, TimeUnit.SECONDS)
            .build()
    }
    
    private val retrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(AppConstants.Api.BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    val apiService: ApiService by lazy {
        retrofit.create(ApiService::class.java)
    }
    
    // Repository dependencies
    val authRepository: AuthRepository by lazy {
        AuthRepositoryImpl(apiService)
    }
    
    // UseCase dependencies
    val loginUseCase: LoginUseCase by lazy {
        LoginUseCase(authRepository)
    }
    
    val registerUseCase: RegisterUseCase by lazy {
        RegisterUseCase(authRepository)
    }
}
